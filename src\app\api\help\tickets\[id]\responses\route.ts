import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { HelpTicket, HelpTicketResponse } from '@/models/HelpTicket';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';

interface TicketQuery {
  _id: string;
  userId?: string;
}

interface TicketUpdateData {
  lastResponseAt: Date;
  lastResponseBy: string;
  updatedAt: Date;
  status?: string;
  $push?: { responses: any };
}

/**
 * GET /api/help/tickets/[id]/responses
 * Get responses for a specific ticket
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await ensureMongooseConnection();

    // Get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { id } = await Promise.resolve(params);

    // Check if user has access to this ticket
    const query: TicketQuery = { _id: id };
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
    }

    const ticket = await HelpTicket.findOne(query);
    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Get responses
    const responses = await HelpTicketResponse.find({ ticketId: id })
      .populate('responderId', 'name email profileImage role')
      .sort({ createdAt: 1 })
      .lean();

    // Filter out internal responses for non-admin users
    const filteredResponses = user.role === 'admin' || user.role === 'superadmin' 
      ? responses 
      : responses.filter(response => !response.isInternal);

    return NextResponse.json({ responses: filteredResponses });

  } catch (error) {
    console.error('Error fetching ticket responses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ticket responses' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/help/tickets/[id]/responses
 * Add a response to a ticket
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await ensureMongooseConnection();

    // Get request body and userId
    const body = await request.json();
    const userId = body.userId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { id } = await Promise.resolve(params);
    const { message, attachments = [], isInternal = false } = body;

    if (!message || message.trim().length === 0) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if user has access to this ticket
    const query: TicketQuery = { _id: id };
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
      // Regular users cannot create internal responses
      if (isInternal) {
        return NextResponse.json(
          { error: 'Forbidden: Cannot create internal responses' },
          { status: 403 }
        );
      }
    }

    const ticket = await HelpTicket.findOne(query);
    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Create the response
    const response = new HelpTicketResponse({
      ticketId: id,
      responderId: userId,
      responderType: user.role === 'admin' || user.role === 'superadmin' ? 'admin' : 'user',
      message: message.trim(),
      attachments,
      isInternal: isInternal && (user.role === 'admin' || user.role === 'superadmin')
    });

    await response.save();

    // Update ticket with last response info
    const updateData: TicketUpdateData = {
      lastResponseAt: new Date(),
      lastResponseBy: userId,
      updatedAt: new Date()
    };

    // If ticket was waiting for user and user responded, change status to open
    if (ticket.status === 'waiting_for_user' && user.role !== 'admin' && user.role !== 'superadmin') {
      updateData.status = 'open';
    }

    // If admin responded and ticket was open, change to in_progress
    if (ticket.status === 'open' && (user.role === 'admin' || user.role === 'superadmin')) {
      updateData.status = 'in_progress';
    }

    // Add response to ticket's responses array
    updateData.$push = { responses: response._id };

    await HelpTicket.findByIdAndUpdate(id, updateData);

    // Populate the response before returning
    const populatedResponse = await HelpTicketResponse.findById(response._id)
      .populate('responderId', 'name email profileImage role')
      .lean();

    return NextResponse.json({
      message: 'Response added successfully',
      response: populatedResponse
    }, { status: 201 });

  } catch (error) {
    console.error('Error adding ticket response:', error);
    return NextResponse.json(
      { error: 'Failed to add ticket response' },
      { status: 500 }
    );
  }
}
