"use client";

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Play, Star, Calendar, Clock, Plus, Info, Check } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ContentCardType } from '@/lib/content-utils';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { toast } from 'sonner';

export interface ContentCardProps extends ContentCardType {
  index?: number; // For staggered animations
  showRating?: boolean;
  showMetadata?: boolean;
  link?: string; // Custom link for the watch button
  listMode?: boolean; // Whether to display in list mode
  description?: string; // Description for list mode
  onClick?: (e: React.MouseEvent) => void; // Optional click handler
  showDataSource?: boolean; // Whether to display the data source (TMDB/OMDB)
}

export default function ContentCard({
  id,
  title,
  imagePath,
  type,
  year,
  ageRating,
  userRating,
  duration,
  index = 0,
  showRating = true,
  showMetadata = true,
  link,
  listMode = false,
  description,
  onClick,
  isAwardWinning,
  dataSource,
  showDataSource = false
}: ContentCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Get router for navigation
  const router = useRouter();

  // Get watchlist functions
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const isInList = isInWatchlist(id);

  // Set mounted state after component mounts on client
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Memoized event handlers to prevent recreating on each render
  const handleMouseEnter = useCallback(() => {
    if (isMounted) {
      setIsHovered(true);
    }
  }, [isMounted]);

  const handleMouseLeave = useCallback(() => {
    if (isMounted) {
      setIsHovered(false);
    }
  }, [isMounted]);

  // Debug logging disabled to avoid excessive console output
  // Enable only when needed for troubleshooting
  /*
  console.log(`[CARD DEBUG] Received content card: ${title} (${id})`);
  console.log(`[CARD DEBUG] Content type: ${type}`);
  console.log(`[CARD DEBUG] Image path: "${imagePath}"`);
  console.log(`[CARD DEBUG] Full props:`, {id, title, imagePath, type, year, ageRating, userRating});
  console.log(`[CARD DEBUG] ID type: ${typeof id}`);
  */

  // Determine link url based on content type, ensuring proper params are passed
  const contentTypeParam = type === 'shows' ? 'show' : 'movie';
  // Use the provided link or construct the correct watch URL
  const watchHref = link || `/watch/${id}?forcePlay=true&contentType=${contentTypeParam}`;

  // Validate ID before constructing details URL
  const isValidId = id !== undefined &&
                  id !== null &&
                  id !== 'undefined' &&
                  id !== 'null' &&
                  id !== '[object Object]' &&
                  String(id).trim() !== '';

  if (!isValidId) {
    console.error(`[ContentCard] Invalid ID detected for ${title}:`, id);
  }

  // Ensure the ID is properly cast to a string and sanitized
  const sanitizedId = !isValidId ? 'unknown' : String(id).trim();

  // Only create a details URL if we have a valid ID
  const detailsHref = !isValidId
    ? '/' // Redirect to home if ID is invalid
    : `/details/${type === 'shows' ? 'shows' : 'movies'}/${sanitizedId}`;

  // Handle link click with custom handler if provided
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(e);
    }
  };

  // Handle image error
  const handleImageError = () => {
    console.error(`Image error for ${title} (${type})`);
    // Don't log the full image path to avoid exposing sensitive URLs
    setImageError(true);

    // Try to report the error for debugging (without exposing sensitive data)
    try {
      const sanitizedImagePath = imagePath ?
        (imagePath.startsWith('http') ?
          new URL(imagePath).pathname.split('/').pop() :
          imagePath.split('/').pop())
        : 'undefined';

      console.debug(`Image load failed for ${title} (${type}). Path ends with: ${sanitizedImagePath}`);
    } catch (e) {
      // Ignore URL parsing errors
    }
  };

  // Use a placeholder image if the image path is empty, null, undefined, or on error
  const actualImagePath = (() => {
    // If we have an error or the received path is invalid, use placeholder
    if (imageError || !imagePath || imagePath.trim() === '') {
      return 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image';
    }

    // Handle TMDb paths that might not be properly formatted
    if (imagePath.startsWith('/') && !imagePath.startsWith('/api/')) {
      // This is likely a TMDb path without the base URL
      return `https://image.tmdb.org/t/p/w342${imagePath}`;
    }

    // Handle relative paths that should go through our wiki-image API
    if (imagePath.startsWith('/') && !imagePath.startsWith('/api/wiki-image')) {
      return `/api/wiki-image?path=${encodeURIComponent(imagePath)}&size=w342`;
    }

    // Trust the incoming imagePath for all other cases
    return imagePath;
  })();

  // Check for mobile on mount
  useEffect(() => {
    setIsMobile(
      window.innerWidth < 768 ||
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    );
  }, []);

  // Render list mode
  if (listMode) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 15 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.4,
          delay: index * 0.05,
          ease: [0.25, 0.1, 0.25, 1.0]
        }}
        className="flex flex-col sm:flex-row items-start sm:items-center bg-vista-dark-lighter rounded-lg p-3 hover:bg-vista-dark-lighter/70 transition-colors duration-200 w-full"
      >
        <div className="relative h-24 w-16 sm:h-20 sm:w-14 flex-shrink-0 rounded overflow-hidden mb-3 sm:mb-0">
          <Image
            src={actualImagePath}
            alt={title || 'Content poster'}
            fill
            className="object-cover"
            loading="lazy"
            onError={handleImageError}
            unoptimized={true} // Always use unoptimized for external images
            referrerPolicy="no-referrer"
          />
        </div>
        <div className="flex-1 sm:ml-4 w-full">
          <h3 className="text-vista-light font-medium text-base sm:text-lg line-clamp-1">{title}</h3>
          {description && (
            <p className="text-vista-light/60 text-xs sm:text-sm mt-1 line-clamp-2">{description}</p>
          )}
          <div className="flex items-center text-vista-light/60 text-xs sm:text-sm mt-1.5 flex-wrap gap-x-1.5">
            {year && <span>{year}</span>}
            {ageRating && (
              <>
                {year && <span className="hidden sm:inline">•</span>}
                <span>{ageRating}</span>
              </>
            )}
            {duration && (
              <>
                {(year || ageRating) && <span className="hidden sm:inline">•</span>}
                <span>{duration}</span>
              </>
            )}
            {userRating && (
              <>
                {(year || ageRating || duration) && <span className="hidden sm:inline">•</span>}
                <span className="flex items-center">
                  <Star className="w-3 h-3 sm:w-3.5 sm:h-3.5 text-yellow-400 fill-yellow-400 mr-0.5 sm:mr-1" />
                  {userRating}
                </span>
              </>
            )}
          </div>
          <div className="mt-3 sm:hidden w-full">
            <Link href={watchHref} onClick={handleClick} className="block w-full">
              <Button variant="default" size="sm" className="w-full bg-vista-blue hover:bg-vista-blue/90 text-white">
                Watch Now
              </Button>
            </Link>
          </div>
        </div>
        <div className="hidden sm:block flex-shrink-0 ml-auto pl-3">
          <Link href={watchHref} onClick={handleClick}>
            <Badge variant="default" className="bg-vista-blue hover:bg-vista-blue/90 text-white px-3 py-1">
              Watch
            </Badge>
          </Link>
        </div>
      </motion.div>
    );
  }

  // Render grid mode (default)
  return (
    <motion.div
      initial={{ opacity: 0, y: 15 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: isMobile ? 0.3 : 0.4,
        delay: isMobile ? Math.min(0.02 * index, 0.3) : Math.min(0.05 * index, 0.5), // Cap the delay on mobile
        ease: [0.25, 0.1, 0.25, 1.0]
      }}
      className="relative overflow-hidden rounded-md"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Card Base with Shadow */}
      <div className={`relative aspect-[2/3] w-full overflow-hidden rounded-md bg-vista-dark-lighter shadow-vista transition-shadow duration-200 ${isHovered ? 'shadow-vista-hover' : ''}`}>
        {/* Image - Restored next/image */}
        <Image
          src={actualImagePath}
          alt={title || 'Content poster'}
          fill
          sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
          className={`object-cover ${!isMobile ? `transition-transform duration-300 ${isHovered ? 'scale-105' : ''}` : ''}`}
          loading="lazy"
          onError={handleImageError}
          unoptimized={true} // Always use unoptimized for external images
          // Add referrerPolicy to fix cross-origin loading issues
          referrerPolicy="no-referrer"
        />

        {/* Gradient Overlay */}
        <div className={`absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent transition-opacity duration-300 ${isHovered ? 'opacity-0' : 'opacity-60'}`} />

        {/* User Rating Badge (if available and should be shown) */}
        {userRating && showRating && (
          <div className="absolute top-2 right-2 z-10">
            <div className="flex items-center gap-0.5 bg-black/70 rounded-full px-1.5 py-0.5 backdrop-blur-sm">
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <span className="text-xs font-medium text-vista-light">{userRating}</span>
            </div>
          </div>
        )}

        {/* Hover overlay with actions - Using state-based hover instead of group hover */}
        <div className={`absolute inset-0 bg-black/80 flex flex-col justify-center items-center gap-2 p-3 transition-opacity duration-200 ${isHovered ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}>
          <Button
            size="sm"
            className="w-full bg-white text-vista-dark hover:bg-white/90 gap-1 transition-colors duration-200 ease-out relative overflow-hidden group"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (onClick) {
                onClick(e);
              } else {
                window.location.href = watchHref;
              }
            }}
          >
            <span className="absolute inset-0 rounded-md bg-white/0 group-hover:bg-white/40 transition-colors duration-300"></span>
            <span className="absolute -inset-px rounded-md border-2 border-white/0 group-hover:border-white/60 group-hover:animate-pulse transition-colors duration-300"></span>
            <Play className="h-3 w-3 relative z-10 transition-transform duration-300 group-hover:scale-110" />
            <span className="relative z-10">Watch</span>
          </Button>

          <Button
            size="sm"
            variant={isInList ? "default" : "outline"}
            className={`w-full gap-1 ${isInList ? 'bg-vista-blue text-white' : 'border-vista-light/30'}`}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (isInList) {
                removeFromWatchlist(id);
                toast(`Removed "${title}" from My List`);
              } else {
                // Add to watchlist

                addToWatchlist({
                  id,
                  title,
                  imagePath,
                  type,
                  year,
                  ageRating,
                  userRating,
                  duration,
                  isAwardWinning,
                  dataSource
                });
                toast(`Added "${title}" to My List`);
              }
            }}
          >
            {isInList ? (
              <>
                <Check className="h-3 w-3" />
                In My List
              </>
            ) : (
              <>
                <Plus className="h-3 w-3" />
                Add to List
              </>
            )}
          </Button>

          <Button
            size="sm"
            variant="ghost"
            className="w-full gap-1 border-vista-light/30 text-vista-light"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              // Validate ID before navigation
              if (!id || id === 'undefined' || id === 'null') {
                console.error(`[ContentCard] Cannot navigate to details: Invalid ID for ${title}:`, id);
                return;
              }

              // Use Next.js router for navigation
              router.push(detailsHref);
            }}
          >
            <Info className="h-3 w-3" />
            Details
          </Button>
        </div>

        {/* Content Overlay (visible when not hovered) */}
        <div className={`absolute inset-x-0 bottom-0 p-3 z-10 transition-opacity duration-200 ${isHovered ? 'opacity-0' : 'opacity-100'}`}>
          {showMetadata && (
            <div className="flex items-center flex-wrap gap-x-1.5 mt-1 text-2xs text-vista-light/70">
              {ageRating && (
                <Badge variant="outline" className="h-4 bg-vista-dark-lighter/40 px-1 py-0 border-vista-light/20 text-[10px]">
                  {ageRating}
                </Badge>
              )}
              {duration && (
                <div className="flex items-center gap-0.5">
                  <Clock className="h-2.5 w-2.5" />
                  <span>{duration}</span>
                </div>
              )}
              {isAwardWinning && (
                <Badge variant="outline" className="h-4 bg-yellow-900/40 px-1 py-0 border-yellow-600/30 text-yellow-500 text-[10px]">
                  Award Winner
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Title and metadata below the card (always visible) */}
      <div className="mt-2">
        <Link href={detailsHref}>
          <h3 className={`font-medium text-vista-light line-clamp-1 transition-colors`}>
            {title}
          </h3>
        </Link>

        {showMetadata && (
          <div className="flex items-center justify-between mt-1">
            <div className="flex items-center gap-2">
              <span className="text-xs text-vista-light/70">{year}</span>
              {showDataSource && dataSource && (
                <Badge variant="outline" className="h-4 bg-vista-dark-lighter/40 px-1 py-0 border-vista-light/20 text-[10px]">
                  {dataSource === 'tmdb' ? 'TMDB' :
                   dataSource === 'omdb' ? 'OMDB' : 'TMDB+OMDB'}
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}
