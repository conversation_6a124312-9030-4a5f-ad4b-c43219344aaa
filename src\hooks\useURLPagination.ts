import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';

interface UseURLPaginationOptions {
  defaultPage?: number;
  pageParamName?: string;
}

export function useURLPagination(options: UseURLPaginationOptions = {}) {
  const { defaultPage = 1, pageParamName = 'page' } = options;
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Get current page from URL or use default
  const currentPage = useMemo(() => {
    if (!searchParams) return defaultPage;
    
    const pageParam = searchParams.get(pageParamName);
    if (pageParam) {
      const page = parseInt(pageParam, 10);
      return isNaN(page) || page < 1 ? defaultPage : page;
    }
    return defaultPage;
  }, [searchParams, pageParamName, defaultPage]);

  // Function to update page in URL
  const setPage = useCallback((page: number) => {
    console.log('[useURLPagination] setPage called with:', page);
    
    if (!searchParams) {
      console.log('[useURLPagination] No searchParams, returning');
      return;
    }
    
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    if (page <= 1) {
      // Remove page parameter if going to page 1 (default)
      current.delete(pageParamName);
    } else {
      current.set(pageParamName, page.toString());
    }

    const search = current.toString();
    const query = search ? `?${search}` : '';
    const newUrl = `${pathname}${query}`;
    
    console.log('[useURLPagination] Navigating to:', newUrl);
    
    // Use replace for same page, push for different pages, and always scroll to top
    router.push(newUrl);
    
    // Use requestAnimationFrame for better performance and timing
    requestAnimationFrame(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }, [searchParams, router, pathname, pageParamName]);

  // Function to reset page to 1 (useful when filters change)
  const resetPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  // Function to get URL with specific page (useful for prefetching)
  const getPageURL = useCallback((page: number) => {
    if (!searchParams) return pathname;
    
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    if (page <= 1) {
      current.delete(pageParamName);
    } else {
      current.set(pageParamName, page.toString());
    }

    const search = current.toString();
    const query = search ? `?${search}` : '';
    
    return `${pathname}${query}`;
  }, [searchParams, pathname, pageParamName]);

  return {
    currentPage,
    setPage,
    resetPage,
    getPageURL
  };
} 